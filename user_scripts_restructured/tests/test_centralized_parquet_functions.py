#!/usr/bin/env python3
"""
Test suite for centralized parquet writing functions.

This test suite validates that all refactored components work correctly
with the centralized nautilus_data_saver utilities.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
import pyarrow as pa
import pyarrow.parquet as pq

# Import the centralized functions
from user_scripts_restructured.core.nautilus_data_saver import (
    create_nautilus_metadata,
    write_parquet_with_metadata,
    validate_parquet_compatibility,
    get_optimal_compression_settings,
    ensure_nanosecond_timestamps,
    create_exchange_mapping,
    get_exchange_for_symbol,
    convert_bars_to_nautilus_format,
    save_bars_to_nautilus_catalog,
    save_table_with_nautilus_metadata,
    consolidate_parquet_files,
    create_fallback_parquet_file,
    ParquetWriteConfig,
    SaveResult
)


class TestCentralizedParquetFunctions:
    """Test suite for centralized parquet functions."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for tests."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def sample_bar_data(self):
        """Create sample bar data for testing."""
        return [
            {
                'open': 15000.0,
                'high': 15010.0,
                'low': 14995.0,
                'close': 15005.0,
                'volume': 100,
                'ts_init': 1640995200000000000,  # 2022-01-01 00:00:00 UTC in nanoseconds
                'ts_event': 1640995200000000000
            },
            {
                'open': 15005.0,
                'high': 15015.0,
                'low': 15000.0,
                'close': 15010.0,
                'volume': 150,
                'ts_init': 1640995260000000000,  # 2022-01-01 00:01:00 UTC in nanoseconds
                'ts_event': 1640995260000000000
            }
        ]
    
    def test_create_nautilus_metadata(self):
        """Test Nautilus metadata creation."""
        metadata = create_nautilus_metadata(
            instrument_id="MNQ.CME",
            bar_type="1-MINUTE-LAST-EXTERNAL",
            price_precision=2,
            size_precision=0
        )
        
        assert metadata['instrument_id'] == "MNQ.CME"
        assert metadata['bar_type'] == "MNQ.CME-1-MINUTE-LAST-EXTERNAL"
        assert metadata['price_precision'] == "2"
        assert metadata['size_precision'] == "0"
    
    def test_parquet_write_config(self):
        """Test ParquetWriteConfig dataclass."""
        config = ParquetWriteConfig()
        assert config.compression == 'snappy'
        assert config.use_dictionary is True
        assert config.row_group_size == 100000
        
        custom_config = ParquetWriteConfig(
            compression='zstd',
            row_group_size=50000
        )
        assert custom_config.compression == 'zstd'
        assert custom_config.row_group_size == 50000
    
    def test_write_parquet_with_metadata(self, temp_dir, sample_bar_data):
        """Test writing parquet with metadata."""
        # Create a simple table
        table = pa.Table.from_pylist(sample_bar_data)
        file_path = Path(temp_dir) / "test_output.parquet"
        
        # Create metadata
        metadata = create_nautilus_metadata("MNQ.CME")
        
        # Write with metadata
        success = write_parquet_with_metadata(
            table, str(file_path), metadata
        )
        
        assert success is True
        assert file_path.exists()
        
        # Verify metadata was written
        written_table = pq.read_table(str(file_path))
        assert written_table.schema.metadata is not None
        # PyArrow stores metadata as bytes, need to decode
        written_metadata = {k.decode() if isinstance(k, bytes) else k:
                           v.decode() if isinstance(v, bytes) else v
                           for k, v in written_table.schema.metadata.items()}
        assert written_metadata['instrument_id'] == "MNQ.CME"
    
    def test_validate_parquet_compatibility(self, sample_bar_data):
        """Test parquet compatibility validation."""
        # Create a valid table
        table = pa.Table.from_pylist(sample_bar_data)
        
        is_valid, issues = validate_parquet_compatibility(table, for_nautilus=True)
        assert is_valid is True
        assert len(issues) == 0
        
        # Create an invalid table (missing required columns)
        invalid_data = [{'price': 100.0, 'qty': 10}]
        invalid_table = pa.Table.from_pylist(invalid_data)
        
        is_valid, issues = validate_parquet_compatibility(invalid_table, for_nautilus=True)
        assert is_valid is False
        assert len(issues) > 0
    
    def test_get_optimal_compression_settings(self):
        """Test optimal compression settings."""
        # Small file
        small_config = get_optimal_compression_settings(5.0)
        assert small_config.compression == 'snappy'
        assert small_config.row_group_size == 50000
        
        # Large file
        large_config = get_optimal_compression_settings(150.0)
        assert large_config.compression == 'zstd'
        assert large_config.row_group_size == 200000
    
    def test_ensure_nanosecond_timestamps(self):
        """Test timestamp conversion to nanoseconds."""
        # Test seconds
        ns_from_seconds = ensure_nanosecond_timestamps(1640995200)
        assert ns_from_seconds == 1640995200000000000
        
        # Test milliseconds
        ns_from_ms = ensure_nanosecond_timestamps(1640995200000)
        assert ns_from_ms == 1640995200000000000
        
        # Test nanoseconds (should remain unchanged)
        ns_from_ns = ensure_nanosecond_timestamps(1640995200000000000)
        assert ns_from_ns == 1640995200000000000
        
        # Test datetime
        dt = datetime(2022, 1, 1, 0, 0, 0)
        ns_from_dt = ensure_nanosecond_timestamps(dt)
        assert isinstance(ns_from_dt, int)
    
    def test_exchange_mapping(self):
        """Test exchange mapping functions."""
        exchange_map = create_exchange_mapping()
        assert exchange_map['MNQ'] == 'CME'
        assert exchange_map['AAPL'] == 'NASDAQ'
        
        # Test symbol resolution
        exchange = get_exchange_for_symbol('MNQ')
        assert exchange == 'CME'
        
        exchange = get_exchange_for_symbol('UNKNOWN_SYMBOL')
        assert exchange == 'SIM'
    
    def test_save_table_with_nautilus_metadata(self, temp_dir, sample_bar_data):
        """Test saving table with Nautilus metadata."""
        table = pa.Table.from_pylist(sample_bar_data)
        file_path = Path(temp_dir) / "test_nautilus.parquet"
        
        success = save_table_with_nautilus_metadata(
            table=table,
            file_path=str(file_path),
            instrument_id="MNQ.CME",
            bar_type="1-MINUTE-LAST-EXTERNAL",
            price_precision=2,
            size_precision=0
        )
        
        assert success is True
        assert file_path.exists()
        
        # Verify Nautilus metadata
        written_table = pq.read_table(str(file_path))
        # PyArrow stores metadata as bytes, need to decode
        metadata = {k.decode() if isinstance(k, bytes) else k:
                   v.decode() if isinstance(v, bytes) else v
                   for k, v in written_table.schema.metadata.items()}
        assert metadata['instrument_id'] == "MNQ.CME"
        assert metadata['bar_type'] == "MNQ.CME-1-MINUTE-LAST-EXTERNAL"
    
    def test_create_fallback_parquet_file(self, temp_dir, sample_bar_data):
        """Test fallback parquet file creation."""
        result = create_fallback_parquet_file(
            data=sample_bar_data,
            symbol="MNQ",
            output_dir=temp_dir,
            add_timestamp=True
        )
        
        assert result.success is True
        assert result.bars_saved == 2
        assert Path(result.file_path).exists()
        
        # Test without timestamp
        result2 = create_fallback_parquet_file(
            data=sample_bar_data,
            symbol="ES",
            output_dir=temp_dir,
            add_timestamp=False
        )
        
        assert result2.success is True
        assert "ES_bars.parquet" in result2.file_path
    
    def test_consolidate_parquet_files(self, temp_dir, sample_bar_data):
        """Test parquet file consolidation."""
        # Create multiple input files
        input_files = []
        for i in range(3):
            file_path = Path(temp_dir) / f"input_{i}.parquet"
            table = pa.Table.from_pylist(sample_bar_data)
            pq.write_table(table, str(file_path))
            input_files.append(str(file_path))
        
        output_file = Path(temp_dir) / "consolidated.parquet"
        
        success, stats = consolidate_parquet_files(
            input_files=input_files,
            output_file=str(output_file),
            instrument_id="MNQ.CME",
            remove_duplicates=False,  # Disable for test simplicity
            sort_by_timestamp=True
        )
        
        assert success is True
        assert output_file.exists()
        assert stats['input_files'] == 3
        assert stats['total_output_rows'] > 0
    
    def test_save_result_dataclass(self):
        """Test SaveResult dataclass."""
        result = SaveResult(
            success=True,
            bars_saved=100,
            file_path="/path/to/file.parquet"
        )
        
        assert result.success is True
        assert result.bars_saved == 100
        assert result.file_path == "/path/to/file.parquet"
        assert result.error_message == ""
        assert result.append_mode_used is False


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
