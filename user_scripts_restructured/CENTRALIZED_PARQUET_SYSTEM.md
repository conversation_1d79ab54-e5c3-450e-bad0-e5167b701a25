# Centralized Parquet Writing System

This document describes the refactored, centralized parquet writing system that provides modular, atomic, and Nautilus-compatible data saving functionality across all user scripts.

## Overview

The system has been completely refactored to eliminate code duplication and provide consistent, reliable parquet writing operations. All scripts now use centralized utility functions instead of implementing their own parquet writing logic.

## Architecture

### Core Components

1. **Utility Functions** (`nautilus_data_saver.py`): Atomic, modular functions for specific operations
2. **Configuration Classes**: Standardized configuration for parquet writing
3. **Validation Functions**: Ensure Nautilus compatibility and data integrity
4. **High-Level Convenience Class**: Simple interface for common use cases

### Key Benefits

- **Zero Code Duplication**: All parquet operations use centralized functions
- **Consistent Metadata**: Standardized Nautilus-compatible metadata across all files
- **Atomic Operations**: Each function performs a single, well-defined task
- **Comprehensive Testing**: Full test coverage for all functionality
- **Modular Design**: Functions can be imported and used independently

## Utility Functions Reference

### Core Writing Functions

```python
from user_scripts_restructured.core.nautilus_data_saver import (
    write_parquet_with_metadata,
    save_table_with_nautilus_metadata,
    create_nautilus_metadata,
    ParquetWriteConfig
)

# Basic parquet writing with metadata
success = write_parquet_with_metadata(
    table=pyarrow_table,
    file_path="output.parquet",
    metadata={"key": "value"},
    config=ParquetWriteConfig()
)

# High-level Nautilus-compatible saving
success = save_table_with_nautilus_metadata(
    table=pyarrow_table,
    file_path="output.parquet",
    instrument_id="MNQ.CME",
    bar_type="1-MINUTE-LAST-EXTERNAL",
    price_precision=2,
    size_precision=0
)
```

### Validation and Utility Functions

```python
from user_scripts_restructured.core.nautilus_data_saver import (
    validate_parquet_compatibility,
    get_optimal_compression_settings,
    ensure_nanosecond_timestamps,
    get_exchange_for_symbol
)

# Validate Nautilus compatibility
is_valid, issues = validate_parquet_compatibility(table, for_nautilus=True)

# Get optimal settings based on file size
config = get_optimal_compression_settings(file_size_mb=50.0)

# Convert timestamps to nanoseconds
ns_timestamp = ensure_nanosecond_timestamps(datetime_obj)

# Get exchange for symbol
exchange = get_exchange_for_symbol("MNQ")  # Returns "CME"
```

### Consolidation and Fallback Functions

```python
from user_scripts_restructured.core.nautilus_data_saver import (
    consolidate_parquet_files,
    create_fallback_parquet_file
)

# Consolidate multiple files
success, stats = consolidate_parquet_files(
    input_files=["file1.parquet", "file2.parquet"],
    output_file="consolidated.parquet",
    instrument_id="MNQ.CME",
    remove_duplicates=True,
    sort_by_timestamp=True
)

# Create fallback file when Nautilus catalog unavailable
result = create_fallback_parquet_file(
    data=bars_data,
    symbol="MNQ",
    output_dir="data",
    add_timestamp=True
)
```

## Configuration Classes

### ParquetWriteConfig

```python
from user_scripts_restructured.core.nautilus_data_saver import ParquetWriteConfig

# Default configuration
config = ParquetWriteConfig()
# compression='snappy', use_dictionary=True, row_group_size=100000

# Custom configuration
config = ParquetWriteConfig(
    compression='zstd',
    use_dictionary=True,
    row_group_size=200000,
    write_statistics=True
)
```

### SaveResult

```python
from user_scripts_restructured.core.nautilus_data_saver import SaveResult

# All save operations return SaveResult
result = save_function(...)

if result.success:
    print(f"Saved {result.bars_saved} bars to {result.file_path}")
    print(f"Append mode used: {result.append_mode_used}")
else:
    print(f"Error: {result.error_message}")
```

## Migration Guide

### Before (Old Pattern)

```python
# OLD: Direct PyArrow usage with manual metadata
import pyarrow.parquet as pq

table = pa.Table.from_pylist(data)
metadata = {
    'bar_type': f"{instrument_id}-1-MINUTE-LAST-EXTERNAL",
    'instrument_id': instrument_id,
    'price_precision': '2',
    'size_precision': '0'
}
table = table.replace_schema_metadata(metadata)
pq.write_table(table, file_path, compression='snappy')
```

### After (New Pattern)

```python
# NEW: Centralized utility function
from user_scripts_restructured.core.nautilus_data_saver import save_table_with_nautilus_metadata

table = pa.Table.from_pylist(data)
success = save_table_with_nautilus_metadata(
    table=table,
    file_path=file_path,
    instrument_id=instrument_id,
    bar_type="1-MINUTE-LAST-EXTERNAL",
    price_precision=2,
    size_precision=0
)
```

## Refactored Scripts

The following scripts have been refactored to use the centralized system:

### 1. process_data.py
- **Function**: `save_data_file()` now uses `write_parquet_with_metadata()`
- **Benefits**: Automatic Nautilus metadata, optimized compression settings
- **Usage**: Automatically detects instrument_id from filename

### 2. consolidator.py
- **Function**: Consolidation operations use `write_parquet_with_metadata()`
- **Benefits**: Consistent metadata handling, optimized settings for large files
- **Usage**: Extracts instrument_id from existing metadata or filename

### 3. continuous_futures.py
- **Function**: Uses `save_table_with_nautilus_metadata()`
- **Benefits**: Proper continuous futures metadata, standardized precision
- **Usage**: Automatic metadata creation for continuous contracts

### 4. base_downloader.py
- **Function**: Fallback method uses `create_fallback_parquet_file()`
- **Benefits**: Consistent fallback behavior, proper error handling
- **Usage**: Graceful degradation when Nautilus catalog unavailable

### 5. create_continuous_futures.py
- **Function**: Uses `validate_parquet_compatibility()` for validation
- **Benefits**: Automatic compatibility checking, better error reporting
- **Usage**: Validates input and output files

## Testing

Comprehensive test suites ensure all functionality works correctly:

```bash
# Run utility function tests
python -m pytest tests/test_centralized_parquet_functions.py -v

# Run integration tests for refactored scripts
python -m pytest tests/test_refactored_scripts.py -v

# Run all tests
python -m pytest tests/ -v
```

## Best Practices

### 1. Use Utility Functions for New Code
```python
# Preferred: Use modular utility functions
from user_scripts_restructured.core.nautilus_data_saver import save_table_with_nautilus_metadata

# Avoid: Direct PyArrow operations
import pyarrow.parquet as pq  # Don't use directly
```

### 2. Always Validate Nautilus Compatibility
```python
is_valid, issues = validate_parquet_compatibility(table, for_nautilus=True)
if not is_valid:
    for issue in issues:
        logger.warning(f"Compatibility issue: {issue}")
```

### 3. Use Appropriate Configuration
```python
# For large files
config = get_optimal_compression_settings(file_size_mb=150.0)

# For small files
config = ParquetWriteConfig(row_group_size=50000)
```

### 4. Handle Errors Gracefully
```python
result = save_table_with_nautilus_metadata(...)
if not result.success:
    logger.error(f"Save failed: {result.error_message}")
    # Implement fallback or retry logic
```

## Performance Considerations

- **Compression**: Automatic selection based on file size
- **Row Groups**: Optimized sizes for different use cases
- **Memory Usage**: Efficient table operations
- **Validation**: Optional validation for performance-critical paths

## Future Enhancements

- **Async Operations**: Support for asynchronous parquet writing
- **Streaming**: Support for streaming large datasets
- **Cloud Storage**: Direct integration with cloud storage backends
- **Schema Evolution**: Automatic schema migration support
